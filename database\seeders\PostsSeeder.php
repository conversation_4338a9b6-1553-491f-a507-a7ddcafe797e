<?php

namespace Database\Seeders;

use App\Models\Post;
use App\Models\User;
use App\Models\Comment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PostsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin and moderator users
        $admin = User::where('email', '<EMAIL>')->first();
        $moderator = User::where('email', '<EMAIL>')->first();
        $users = User::role('user')->get();

        // Create posts by admin
        $this->createPost(
            $admin->id,
            'Welcome to Guzexs Gaming',
            'welcome-to-guzexs-gaming',
            '<p>Welcome to the official Guzexs Gaming website! We are excited to launch our new platform dedicated to gaming enthusiasts around the world.</p><p>Here you will find the latest gaming news, reviews, tutorials, and a vibrant community of fellow gamers. Feel free to explore the site, create an account, and join our growing community.</p><p>Stay tuned for more updates and exciting content coming soon!</p>',
            'Welcome to the official Guzexs Gaming website! We are excited to launch our new platform dedicated to gaming enthusiasts around the world.',
            true
        );

        $this->createPost(
            $admin->id,
            'Upcoming Gaming Events in 2024',
            'upcoming-gaming-events-2024',
            '<p>The gaming world is buzzing with excitement as we look forward to several major events in 2024. Here are some of the most anticipated gaming conventions and expos:</p><ul><li><strong>E3 2024</strong> - Los Angeles, June 11-13</li><li><strong>Gamescom 2024</strong> - Cologne, August 21-25</li><li><strong>Tokyo Game Show 2024</strong> - Tokyo, September 26-29</li><li><strong>PAX East 2024</strong> - Boston, April 18-21</li></ul><p>Which events are you planning to attend? Let us know in the comments!</p>',
            'The gaming world is buzzing with excitement as we look forward to several major events in 2024.',
            true
        );

        // Create posts by moderator
        $this->createPost(
            $moderator->id,
            'Top 5 Gaming PCs of 2024',
            'top-5-gaming-pcs-2024',
            '<p>Looking to upgrade your gaming setup? Here are our top picks for gaming PCs in 2024:</p><ol><li><strong>Alienware Aurora R15</strong> - Ultimate performance with the latest RTX 4090</li><li><strong>ASUS ROG Strix G35</strong> - Perfect balance of performance and price</li><li><strong>MSI MEG Aegis Ti5</strong> - Innovative design with top-tier components</li><li><strong>Corsair One i300</strong> - Compact powerhouse for limited spaces</li><li><strong>HP Omen 45L</strong> - Excellent cooling and expandability</li></ol><p>What's your dream gaming PC? Share your thoughts below!</p>',
            'Looking to upgrade your gaming setup? Here are our top picks for gaming PCs in 2024.',
            true
        );

        // Create a draft post
        $this->createPost(
            $admin->id,
            'The Future of Cloud Gaming',
            'future-of-cloud-gaming',
            '<p>Cloud gaming has been gaining momentum in recent years, but what does the future hold for this technology? In this article, we will explore the potential developments and challenges facing cloud gaming platforms.</p><p>[This is a draft post with more content to be added later]</p>',
            'Cloud gaming has been gaining momentum in recent years, but what does the future hold for this technology?',
            false
        );

        // Add comments to posts
        $posts = Post::where('is_published', true)->get();
        
        foreach ($posts as $post) {
            // Add some comments from regular users
            foreach ($users->random(rand(2, 4)) as $user) {
                $comment = Comment::create([
                    'user_id' => $user->id,
                    'commentable_id' => $post->id,
                    'commentable_type' => Post::class,
                    'content' => $this->getRandomComment(),
                ]);
                
                // Add replies to some comments
                if (rand(0, 1)) {
                    $replier = $users->except($user->id)->random();
                    Comment::create([
                        'user_id' => $replier->id,
                        'parent_id' => $comment->id,
                        'commentable_id' => $post->id,
                        'commentable_type' => Post::class,
                        'content' => $this->getRandomReply(),
                    ]);
                }
            }
        }
    }

    /**
     * Helper method to create a post
     */
    private function createPost($userId, $title, $slug, $content, $excerpt, $isPublished)
    {
        return Post::create([
            'user_id' => $userId,
            'title' => $title,
            'slug' => $slug,
            'content' => $content,
            'excerpt' => $excerpt,
            'is_published' => $isPublished,
            'published_at' => $isPublished ? now() : null,
        ]);
    }

    /**
     * Get a random comment
     */
    private function getRandomComment()
    {
        $comments = [
            'Great article! Thanks for sharing this information.',
            'I\'ve been waiting for content like this. Very informative!',
            'This is exactly what I needed to know. Looking forward to more posts like this.',
            'Interesting perspective. I never thought about it this way before.',
            'Do you have any more resources on this topic? I\'d love to learn more.',
            'This is awesome! Can\'t wait to see what comes next.',
            'I\'ve been following this topic for a while, and this is the best explanation I\'ve seen.',
            'Thanks for the detailed breakdown. Very helpful!',
        ];

        return $comments[array_rand($comments)];
    }

    /**
     * Get a random reply
     */
    private function getRandomReply()
    {
        $replies = [
            'I agree with your comment! Very insightful.',
            'Thanks for sharing your thoughts on this.',
            'That\'s a good point. I hadn\'t considered that aspect.',
            'I had the same reaction when I first read this.',
            'Have you tried the suggestions in the article? They worked well for me.',
            'I\'m glad someone else feels the same way!',
            'Let\'s connect and discuss this further. I have some additional insights.',
            'This comment section is as valuable as the article itself!',
        ];

        return $replies[array_rand($replies)];
    }
}
