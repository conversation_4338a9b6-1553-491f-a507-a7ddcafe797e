<?php

namespace App\Notifications;

use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommentReplied extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The comment reply instance.
     *
     * @var \App\Models\Comment
     */
    protected $reply;

    /**
     * Create a new notification instance.
     */
    public function __construct(Comment $reply)
    {
        $this->reply = $reply;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $commentable = $this->reply->commentable;
        $url = '';
        
        // Determine the URL based on commentable type
        if (get_class($commentable) === 'App\\Models\\Post') {
            $url = route('posts.show', $commentable->slug) . '#comment-' . $this->reply->id;
        }
        
        return (new MailMessage)
            ->subject('Ada Balasan Komentar Baru di Guzexs Gaming')
            ->greeting('Halo ' . $notifiable->name . '!')
            ->line($this->reply->user->name . ' membalas komentar Anda:')
            ->line('"' . substr($this->reply->content, 0, 100) . (strlen($this->reply->content) > 100 ? '...' : '') . '"')
            ->action('Lihat Balasan', $url)
            ->line('Terima kasih telah berpartisipasi di komunitas Guzexs Gaming!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'reply_id' => $this->reply->id,
            'user_id' => $this->reply->user_id,
            'user_name' => $this->reply->user->name,
            'commentable_id' => $this->reply->commentable_id,
            'commentable_type' => $this->reply->commentable_type,
        ];
    }
}