<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use App\Models\User;
use App\Notifications\CommentReplied;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class CommentController extends Controller
{
    /**
     * Store a newly created comment.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'content' => 'required|string',
            'commentable_id' => 'required|integer',
            'commentable_type' => 'required|string',
        ]);
        
        // Verify the commentable exists
        $commentableType = $validated['commentable_type'];
        $commentableId = $validated['commentable_id'];
        $commentableClass = "\\App\\Models\\" . $commentableType;
        
        if (!class_exists($commentableClass)) {
            return back()->with('error', 'Invalid comment target.');
        }
        
        $commentable = $commentableClass::find($commentableId);
        
        if (!$commentable) {
            return back()->with('error', 'Comment target not found.');
        }
        
        $comment = new Comment();
        $comment->user_id = Auth::id();
        $comment->content = $validated['content'];
        $comment->commentable_id = $commentableId;
        $comment->commentable_type = $commentableClass;
        $comment->save();
        
        return back()->with('success', 'Komentar berhasil ditambahkan.');
    }

    /**
     * Store a reply to a comment.
     */
    public function reply(Request $request, Comment $comment): RedirectResponse
    {
        $validated = $request->validate([
            'content' => 'required|string',
        ]);
        
        $reply = new Comment();
        $reply->user_id = Auth::id();
        $reply->parent_id = $comment->id;
        $reply->content = $validated['content'];
        $reply->commentable_id = $comment->commentable_id;
        $reply->commentable_type = $comment->commentable_type;
        $reply->save();
        
        // Notify the original comment author about the reply
        if ($comment->user_id != Auth::id()) {
            $comment->user->notify(new CommentReplied($reply));
        }
        
        return back()->with('success', 'Balasan komentar berhasil ditambahkan.');
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment): RedirectResponse
    {
        // Check if user is authorized to update this comment
        if ($comment->user_id !== Auth::id()) {
            return back()->with('error', 'Anda tidak memiliki izin untuk mengedit komentar ini.');
        }
        
        $validated = $request->validate([
            'content' => 'required|string',
        ]);
        
        $comment->content = $validated['content'];
        $comment->save();
        
        return back()->with('success', 'Komentar berhasil diperbarui.');
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment): RedirectResponse
    {
        // Check if user is authorized to delete this comment
        if ($comment->user_id !== Auth::id() && !Auth::user()->hasRole('admin')) {
            return back()->with('error', 'Anda tidak memiliki izin untuk menghapus komentar ini.');
        }
        
        $comment->delete();
        
        return back()->with('success', 'Komentar berhasil dihapus.');
    }
}