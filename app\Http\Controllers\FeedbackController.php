<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class FeedbackController extends Controller
{
    /**
     * Display the feedback form and approved feedback.
     */
    public function index(): View
    {
        // Get approved feedback to display
        $approvedFeedback = Feedback::where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('feedback.index', [
            'approvedFeedback' => $approvedFeedback,
        ]);
    }

    /**
     * Store a newly created feedback.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'message' => 'required|string',
            'is_anonymous' => 'boolean',
        ]);
        
        $feedback = new Feedback();
        
        // If user is logged in and not anonymous
        if (Auth::check() && !($request->is_anonymous ?? false)) {
            $feedback->user_id = Auth::id();
            // No need for name and email as we have user_id
        } else {
            // For anonymous or non-logged in users
            $feedback->name = $validated['name'] ?? null;
            $feedback->email = $validated['email'] ?? null;
            $feedback->is_anonymous = true;
        }
        
        $feedback->message = $validated['message'];
        $feedback->save();
        
        return redirect()->route('feedback.index')
            ->with('success', 'Terima kasih atas feedback Anda. Feedback akan ditampilkan setelah disetujui oleh admin.');
    }

    /**
     * Display all feedback for admin.
     */
    public function adminIndex(): View
    {
        $pendingFeedback = Feedback::where('is_approved', false)
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        $approvedFeedback = Feedback::where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('admin.feedback.index', [
            'pendingFeedback' => $pendingFeedback,
            'approvedFeedback' => $approvedFeedback,
        ]);
    }

    /**
     * Approve a feedback.
     */
    public function approve(Feedback $feedback): RedirectResponse
    {
        $feedback->is_approved = true;
        $feedback->save();
        
        return redirect()->route('admin.feedback.index')
            ->with('success', 'Feedback berhasil disetujui.');
    }

    /**
     * Remove a feedback.
     */
    public function destroy(Feedback $feedback): RedirectResponse
    {
        $feedback->delete();
        
        return redirect()->route('admin.feedback.index')
            ->with('success', 'Feedback berhasil dihapus.');
    }
}