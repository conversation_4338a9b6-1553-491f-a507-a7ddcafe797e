<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Post permissions
            'create posts',
            'edit posts',
            'delete posts',
            'publish posts',
            'unpublish posts',
            
            // Comment permissions
            'create comments',
            'edit comments',
            'delete comments',
            'moderate comments',
            
            // Feedback permissions
            'view feedback',
            'approve feedback',
            'delete feedback',
            
            // User permissions
            'view users',
            'edit users',
            'delete users',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $role = Role::create(['name' => 'admin']);
        $role->givePermissionTo(Permission::all());

        $role = Role::create(['name' => 'moderator']);
        $role->givePermissionTo([
            'create posts',
            'edit posts',
            'publish posts',
            'unpublish posts',
            'create comments',
            'edit comments',
            'delete comments',
            'moderate comments',
            'view feedback',
            'approve feedback',
            'delete feedback',
        ]);

        $role = Role::create(['name' => 'user']);
        $role->givePermissionTo([
            'create comments',
            'edit comments', // Can only edit own comments, enforced in policy
        ]);
    }
}
