<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Call seeders in the correct order
        $this->call(RolesAndPermissionsSeeder::class); // First create roles and permissions
        $this->call(UsersSeeder::class);               // Then create users with roles
        $this->call(PostsSeeder::class);               // Finally create posts and comments
    }
}
