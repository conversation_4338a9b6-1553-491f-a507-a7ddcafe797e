<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin Guzexs',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'bio' => 'Administrator of Guzexs Gaming Website',
            'privacy_settings' => json_encode(['profile_visibility' => 'public']),
            'notification_settings' => json_encode(['email_notifications' => true, 'comment_replies' => true]),
        ]);
        $admin->assignRole('admin');

        // Create moderator user
        $moderator = User::create([
            'name' => 'Moderator Guzexs',
            'username' => 'moderator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'bio' => 'Moderator of Guzexs Gaming Website',
            'privacy_settings' => json_encode(['profile_visibility' => 'public']),
            'notification_settings' => json_encode(['email_notifications' => true, 'comment_replies' => true]),
        ]);
        $moderator->assignRole('moderator');

        // Create regular users
        $user1 = User::create([
            'name' => 'John Doe',
            'username' => 'johndoe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'bio' => 'Gaming enthusiast and streamer',
            'privacy_settings' => json_encode(['profile_visibility' => 'public']),
            'notification_settings' => json_encode(['email_notifications' => true, 'comment_replies' => true]),
        ]);
        $user1->assignRole('user');

        $user2 = User::create([
            'name' => 'Jane Smith',
            'username' => 'janesmith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'bio' => 'Professional gamer and content creator',
            'privacy_settings' => json_encode(['profile_visibility' => 'public']),
            'notification_settings' => json_encode(['email_notifications' => true, 'comment_replies' => true]),
        ]);
        $user2->assignRole('user');

        // Create more users with factory
        User::factory(5)->create()->each(function ($user) {
            $user->assignRole('user');
            $user->privacy_settings = json_encode(['profile_visibility' => 'public']);
            $user->notification_settings = json_encode(['email_notifications' => true, 'comment_replies' => true]);
            $user->save();
        });
    }
}
